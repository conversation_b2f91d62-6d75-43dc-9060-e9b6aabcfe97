<?php
if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];
?>
<!-- Contact Form Section -->
<div class="contact-form">
   <form id="contactForm" data-wow-delay="0.2s">
      <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
      <div class="row">
         <!-- Full Name -->
         <div class="form-group col-md-12 mb-4">
            <input type="text" name="full_name" class="form-control" placeholder="Full Name" required>
         </div>

         <!-- Email -->
         <div class="form-group col-md-6 mb-4">
            <input type="email" name="email" class="form-control" placeholder="Email Address" required>
         </div>

         <!-- Phone -->
         <div class="form-group col-md-6 mb-4">
            <input type="text" name="phone" class="form-control" placeholder="Mobile No." required>
         </div>

         <!-- Message -->
         <div class="form-group col-md-12 mb-5">
            <textarea name="message" class="form-control" rows="4" placeholder="Write your message" required></textarea>
         </div>

         <!-- reCAPTCHA -->
         <div class="form-group col-md-12 mb-3">
            <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
         </div>

         <!-- Submit -->
         <div class="col-md-12">
            <button type="submit" class="btn-default"><span>Send Message</span></button>
         </div>
      </div>
   </form>
</div>

<!-- JavaScript Submission Handler -->
<script>
   document.getElementById("contactForm").addEventListener("submit", function(e) {
      e.preventDefault();

      const form = e.target;
      const formData = new FormData(form);
      const submitBtn = form.querySelector('button[type="submit"]');
      const originalBtnText = submitBtn.innerHTML;

      // Validate form fields before submission
      const fullName = formData.get('full_name')?.trim();
      const email = formData.get('email')?.trim();
      const phone = formData.get('phone')?.trim();
      const message = formData.get('message')?.trim();

      // Check if all required fields are filled
      if (!fullName || !email || !phone || !message) {
         Swal.fire({
            icon: "warning",
            title: "📝 Complete All Fields",
            html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
               <p style='margin-bottom: 15px; font-size: 16px;'>Please fill in all required fields to continue.</p>
               <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                  <p style='margin: 0; font-size: 14px; color: #828282;'>📋 All fields are required for us to respond to your inquiry</p>
               </div>
            </div>`,
            background: "#FFFFFF",
            color: "#224520",
            confirmButtonColor: "#f15e25",
            confirmButtonText: "Got It! ✏️",
            customClass: {
               popup: 'swal2-popup-custom',
               title: 'swal2-title-custom',
               confirmButton: 'swal2-btn-custom'
            }
         });
         return;
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
         Swal.fire({
            icon: "warning",
            title: "📧 Invalid Email Format",
            html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
               <p style='margin-bottom: 15px; font-size: 16px;'>Please enter a valid email address.</p>
               <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                  <p style='margin: 0; font-size: 14px; color: #828282;'>✉️ Example: <EMAIL></p>
               </div>
            </div>`,
            background: "#FFFFFF",
            color: "#224520",
            confirmButtonColor: "#f15e25",
            confirmButtonText: "Fix Email 📧",
            customClass: {
               popup: 'swal2-popup-custom',
               title: 'swal2-title-custom',
               confirmButton: 'swal2-btn-custom'
            }
         });
         return;
      }

      // Validate phone number (basic validation for Indian numbers)
      const phoneRegex = /^[6-9]\d{9}$/;
      const cleanPhone = phone.replace(/\D/g, '').slice(-10);
      if (!phoneRegex.test(cleanPhone)) {
         Swal.fire({
            icon: "warning",
            title: "📱 Invalid Phone Number",
            html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
               <p style='margin-bottom: 15px; font-size: 16px;'>Please enter a valid 10-digit mobile number.</p>
               <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                  <p style='margin: 0; font-size: 14px; color: #828282;'>📞 Example: 9876543210</p>
               </div>
            </div>`,
            background: "#FFFFFF",
            color: "#224520",
            confirmButtonColor: "#f15e25",
            confirmButtonText: "Fix Number 📱",
            customClass: {
               popup: 'swal2-popup-custom',
               title: 'swal2-title-custom',
               confirmButton: 'swal2-btn-custom'
            }
         });
         return;
      }

      submitBtn.innerHTML = '<span>📤 Sending...</span>';
      submitBtn.disabled = true;

      // Get reCAPTCHA response and append to formData
      var recaptchaResponse = grecaptcha.getResponse();
      if (!recaptchaResponse) {
         Swal.fire({
            icon: "warning",
            title: "🤖 Security Verification Required",
            html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
               <p style='margin-bottom: 15px; font-size: 16px;'>Please complete the reCAPTCHA verification to continue.</p>
               <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                  <p style='margin: 0; font-size: 14px; color: #828282;'>🔒 This helps us protect against spam and automated submissions</p>
               </div>
               <p style='font-size: 14px; color: #224520; margin-top: 15px;'>Please scroll down and complete the verification, then try again.</p>
            </div>`,
            background: "#FFFFFF",
            color: "#224520",
            confirmButtonColor: "#f15e25",
            confirmButtonText: "Got It! 👍",
            customClass: {
               popup: 'swal2-popup-custom',
               title: 'swal2-title-custom',
               confirmButton: 'swal2-btn-custom'
            },
            showClass: {
               popup: 'animate__animated animate__fadeInUp'
            }
         });
         submitBtn.innerHTML = originalBtnText;
         submitBtn.disabled = false;
         return;
      }
      formData.append('g-recaptcha-response', recaptchaResponse);

      fetch("../webhook/contact-webhook.php", {
            method: "POST",
            body: formData
         })
         .then(response => response.json())
         .then(data => {
            if (data.success) {
               Swal.fire({
                  icon: "success",
                  title: "🎉 Thank You for Reaching Out!",
                  html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
                     <p style='margin-bottom: 15px; font-size: 16px;'>Your message has been submitted successfully!</p>
                     <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                        <p style='margin: 5px 0; font-size: 14px; color: #828282;'>📧 We've received your message and will get back to you soon</p>
                        <p style='margin: 5px 0; font-size: 14px; color: #828282;'>⏰ Expected response time: Within 24 hours</p>
                     </div>
                     <p style='font-size: 14px; color: #224520; font-weight: 600; margin-top: 15px;'>Thank you for connecting with UnnyanPath Foundation! 🤝</p>
                  </div>`,
                  background: "#FFFFFF",
                  color: "#224520",
                  confirmButtonColor: "#f15e25",
                  confirmButtonText: "Great! 👍",
                  customClass: {
                     popup: 'swal2-popup-custom',
                     title: 'swal2-title-custom',
                     confirmButton: 'swal2-btn-custom'
                  },
                  showClass: {
                     popup: 'animate__animated animate__fadeInUp'
                  },
                  timer: 5000,
                  timerProgressBar: true
               });
               form.reset();
               grecaptcha.reset();
            } else {
               Swal.fire({
                  icon: "error",
                  title: "⚠️ Message Submission Failed",
                  html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
                     <p style='margin-bottom: 15px; font-size: 16px;'>We encountered an issue while sending your message.</p>
                     <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                        <p style='margin: 0; font-size: 14px; color: #f15e25; font-weight: 600;'>${data.error || "Submission failed. Please try again."}</p>
                     </div>
                     <p style='font-size: 14px; color: #828282; margin-top: 15px;'>💡 Please check your details and try again, or contact us directly.</p>
                     <p style='font-size: 14px; color: #224520; margin-top: 10px;'>📞 Alternative: Call us at your convenience</p>
                  </div>`,
                  background: "#FFFFFF",
                  color: "#224520",
                  confirmButtonColor: "#f15e25",
                  confirmButtonText: "Try Again 🔄",
                  customClass: {
                     popup: 'swal2-popup-custom',
                     title: 'swal2-title-custom',
                     confirmButton: 'swal2-btn-custom'
                  },
                  showClass: {
                     popup: 'animate__animated animate__fadeInUp'
                  }
               });
            }
         })
         .catch(() => {
            Swal.fire({
               icon: "error",
               title: "🔌 Connection Error",
               html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
                  <p style='margin-bottom: 15px; font-size: 16px;'>Unable to connect to our servers.</p>
                  <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                     <p style='margin: 0; font-size: 14px; color: #828282;'>🌐 Please check your internet connection and try again</p>
                  </div>
                  <p style='font-size: 14px; color: #224520; margin-top: 15px;'>If the problem persists, please contact us directly.</p>
                  <p style='font-size: 14px; color: #828282; margin-top: 10px;'>📧 Email: <EMAIL></p>
               </div>`,
               background: "#FFFFFF",
               color: "#224520",
               confirmButtonColor: "#f15e25",
               confirmButtonText: "Retry 🔄",
               customClass: {
                  popup: 'swal2-popup-custom',
                  title: 'swal2-title-custom',
                  confirmButton: 'swal2-btn-custom'
               },
               showClass: {
                  popup: 'animate__animated animate__fadeInUp'
               }
            });
         })
         .finally(() => {
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
         });
   });
</script>

<!-- Load Google reCAPTCHA JS -->
<script src="https://www.google.com/recaptcha/api.js" async defer></script>