<?php
$page_title = "Donate | Unnyanpath Foundation - Support Our Mission";
$meta_description = "Support Unnyanpath Foundation's mission by making a donation. Your contribution helps us empower communities and promote education in Uttar Pradesh.";

if (empty($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}
$csrf_token = $_SESSION['csrf_token'];
?>
<!-- Page Header Start -->
<div class="page-header parallaxie">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-12">
                <!-- Page Header Box Start -->
                <div class="page-header-box">
                    <h1 class="text-anime-style-2" data-cursor="-opaque">Donation</h1>
                    <nav class="wow fadeInUp">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?= base_url ?>home">home</a></li>
                            <li class="breadcrumb-item active" aria-current="page">donation</li>
                        </ol>
                    </nav>
                </div>
                <!-- Page Header Box End -->
            </div>
        </div>
    </div>
</div>
<!-- Page Header End -->

<!-- Page Donation Start -->
<div class="page-donation">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <div class="donation-box">
                    <div class="section-title">
                        <h3 class="wow fadeInUp">donate now</h3>
                        <h2 class="text-anime-style-2" data-cursor="-opaque">Your donation</h2>
                        <p class="wow fadeInUp" data-wow-delay="0.2s">
                            Your donation is more than just financial support; it is a powerful act of kindness that drives meaningful change. Every contribution helps provide essential resources, support impactful programs, and empower communities in need.
                        </p>
                    </div>

                    <!-- Donation Form -->
                    <div class="donate-form campaign-donate-form">
                        <form id="donateForm">
                            <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($csrf_token); ?>">
                            <!-- Donation Amount -->
                            <div class="campaign-donate-value wow fadeInUp" data-wow-delay="0.4s">
                                <!-- Donation Amount -->
                                <div class="col-md-12 mb-4">
                                    <label><strong>Enter Amount (in ₹)</strong></label>
                                    <input type="number" name="donation_amount" id="donation_amount" class="form-control" placeholder="Enter amount" min="1" required>
                                </div>

                                <fieldset class="donate-value-box">
                                    <div class="donate-value">
                                        <input type="radio" id="value1" name="preset_amount" value="100">
                                        <label for="value1">₹ 100</label>
                                    </div>
                                    <div class="donate-value">
                                        <input type="radio" id="value2" name="preset_amount" value="500">
                                        <label for="value2">₹ 500</label>
                                    </div>
                                    <div class="donate-value">
                                        <input type="radio" id="value3" name="preset_amount" value="1000">
                                        <label for="value3">₹ 1,000</label>
                                    </div>
                                    <div class="donate-value">
                                        <input type="radio" id="value4" name="preset_amount" value="2000">
                                        <label for="value4">₹ 2,000</label>
                                    </div>
                                    <div class="donate-value">
                                        <input type="radio" id="value5" name="preset_amount" value="5000">
                                        <label for="value5">₹ 5,000</label>
                                    </div>
                                    <div class="donate-value">
                                        <input type="radio" id="value6" name="preset_amount" value="10000">
                                        <label for="value6">₹ 10,000</label>
                                    </div>
                                </fieldset>
                            </div>

                            <!-- Personal Info -->
                            <div class="donar-personal-info">
                                <div class="section-title">
                                    <h2 class="text-anime-style-2" data-cursor="-opaque">Personal <span>info</span></h2>
                                </div>
                                <div class="row wow fadeInUp" data-wow-delay="0.8s">
                                    <div class="row wow fadeInUp" data-wow-delay="0.8s">
                                        <div class="form-group col-md-6 mb-4">
                                            <input type="text" name="name" class="form-control" placeholder=" Enter Your Full Name" required>
                                        </div>
                                        <div class="form-group col-md-6 mb-4">
                                            <input type="email" name="email" class="form-control" placeholder="Enter Your E-mail" required>
                                        </div>
                                        <div class="form-group col-md-6 mb-4">
                                            <input type="text" name="text" class="form-control" placeholder="Enter Your PAN No." required>
                                        </div>
                                        <div class="form-group col-md-6 mb-4">
                                            <input type="text" name="phone" class="form-control" placeholder="Enter Your Phone No." required>
                                        </div>
                                        <div class="form-group col-md-6 mb-5">
                                            <textarea name="address" class="form-control" rows="2" placeholder=" Enter Your Address"></textarea>
                                        </div>
                                        <div class="form-group col-md-6 mb-5">
                                            <textarea name="message" class="form-control" rows="2" placeholder="Write a message (optional)"></textarea>
                                        </div>
                                    </div>
                                </div>

                                <!-- reCAPTCHA Widget -->
                                <div class="form-group col-md-12 mb-3">
                                    <div class="g-recaptcha" data-sitekey="<?php echo RECAPTCHA_SITE_KEY; ?>"></div>
                                </div>
                                <!-- Submit Button -->
                                <div class="form-group col-md-12">
                                    <button type="submit" class="btn-default"><span>Donate</span></button>
                                    <div id="msgSubmit" class="h3 hidden"></div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <!-- Donation Form End -->
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Page Donation End -->

<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script src="https://www.google.com/recaptcha/api.js" async defer></script>

<!-- Custom Razorpay Theme Styles -->
<style>
/* Razorpay Modal Theme Customization */
.razorpay-container {
    font-family: "Nunito Sans", sans-serif !important;
}

.razorpay-container .header {
    background: linear-gradient(135deg, #224520 0%, #2d5a28 100%) !important;
    border-radius: 12px 12px 0 0 !important;
}

.razorpay-container .header .title {
    color: #FFFFFF !important;
    font-family: "Nunito Sans", sans-serif !important;
    font-weight: 700 !important;
}

.razorpay-container .header .subtitle {
    color: #F8F8F8 !important;
    font-family: "Inter", sans-serif !important;
}

.razorpay-container .content {
    background: #FFFFFF !important;
    border-radius: 0 0 12px 12px !important;
}

.razorpay-container .methods {
    background: #F8F8F8 !important;
    border-radius: 8px !important;
    margin: 10px !important;
}

.razorpay-container .method {
    border: 1px solid #020D1914 !important;
    border-radius: 8px !important;
    margin: 5px !important;
    transition: all 0.3s ease-in-out !important;
}

.razorpay-container .method:hover {
    border-color: #f15e25 !important;
    box-shadow: 0 2px 8px rgba(241, 94, 37, 0.15) !important;
}

.razorpay-container .method.focused {
    border-color: #f15e25 !important;
    box-shadow: 0 0 0 2px rgba(241, 94, 37, 0.2) !important;
}

.razorpay-container .method .text {
    color: #224520 !important;
    font-family: "Inter", sans-serif !important;
    font-weight: 600 !important;
}

.razorpay-container .btn-primary {
    background: linear-gradient(135deg, #f15e25 0%, #e54d1c 100%) !important;
    border: none !important;
    border-radius: 100px !important;
    font-family: "Nunito Sans", sans-serif !important;
    font-weight: 700 !important;
    font-size: 16px !important;
    padding: 15px 40px !important;
    transition: all 0.3s ease-in-out !important;
    text-transform: capitalize !important;
}

.razorpay-container .btn-primary:hover {
    background: linear-gradient(135deg, #224520 0%, #2d5a28 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(241, 94, 37, 0.3) !important;
}

.razorpay-container .btn-primary:active {
    transform: translateY(0) !important;
}

.razorpay-container .input {
    border: 1px solid #020D1914 !important;
    border-radius: 8px !important;
    font-family: "Inter", sans-serif !important;
    color: #224520 !important;
    padding: 12px 16px !important;
}

.razorpay-container .input:focus {
    border-color: #f15e25 !important;
    box-shadow: 0 0 0 2px rgba(241, 94, 37, 0.2) !important;
    outline: none !important;
}

.razorpay-container .input::placeholder {
    color: #828282 !important;
}

.razorpay-container .footer {
    background: #F8F8F8 !important;
    border-top: 1px solid #020D1914 !important;
    border-radius: 0 0 12px 12px !important;
}

.razorpay-container .footer .text {
    color: #828282 !important;
    font-family: "Inter", sans-serif !important;
    font-size: 12px !important;
}

.razorpay-container .footer .link {
    color: #f15e25 !important;
    font-weight: 600 !important;
}

.razorpay-container .footer .link:hover {
    color: #224520 !important;
}

/* Loading spinner customization */
.razorpay-container .spinner {
    border-color: #f15e25 !important;
}

/* Error states */
.razorpay-container .error {
    color: #f15e25 !important;
    font-family: "Inter", sans-serif !important;
}

/* Success states */
.razorpay-container .success {
    color: #224520 !important;
    font-family: "Inter", sans-serif !important;
}

/* Modal overlay */
.razorpay-backdrop {
    background: rgba(34, 69, 32, 0.8) !important;
    backdrop-filter: blur(4px) !important;
}

/* Close button */
.razorpay-container .close {
    color: #828282 !important;
    font-size: 24px !important;
    transition: all 0.3s ease-in-out !important;
}

.razorpay-container .close:hover {
    color: #f15e25 !important;
    transform: scale(1.1) !important;
}

/* Payment method icons */
.razorpay-container .method .icon {
    filter: brightness(0.8) !important;
}

.razorpay-container .method.focused .icon,
.razorpay-container .method:hover .icon {
    filter: brightness(1) !important;
}

/* Amount display */
.razorpay-container .amount {
    color: #224520 !important;
    font-family: "Nunito Sans", sans-serif !important;
    font-weight: 700 !important;
    font-size: 24px !important;
}

/* Merchant info */
.razorpay-container .merchant {
    color: #224520 !important;
    font-family: "Nunito Sans", sans-serif !important;
    font-weight: 600 !important;
}

/* Additional responsive styles */
@media (max-width: 768px) {
    .razorpay-container {
        margin: 10px !important;
        border-radius: 12px !important;
    }

    .razorpay-container .btn-primary {
        padding: 12px 30px !important;
        font-size: 14px !important;
    }
}

/* Enhanced Razorpay Modal Styling */
.razorpay-container .header .logo {
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

.razorpay-container .method .bank-logo,
.razorpay-container .method .wallet-logo {
    border-radius: 4px !important;
}

.razorpay-container .tabs {
    background: #F8F8F8 !important;
    border-radius: 8px 8px 0 0 !important;
}

.razorpay-container .tab {
    color: #828282 !important;
    font-family: "Inter", sans-serif !important;
    font-weight: 600 !important;
    border-bottom: 2px solid transparent !important;
    transition: all 0.3s ease-in-out !important;
}

.razorpay-container .tab.active {
    color: #f15e25 !important;
    border-bottom-color: #f15e25 !important;
    background: rgba(241, 94, 37, 0.05) !important;
}

.razorpay-container .tab:hover {
    color: #224520 !important;
    background: rgba(34, 69, 32, 0.05) !important;
}

/* Payment form styling */
.razorpay-container .form-group {
    margin-bottom: 20px !important;
}

.razorpay-container .form-group label {
    color: #224520 !important;
    font-family: "Inter", sans-serif !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    margin-bottom: 8px !important;
}

.razorpay-container .checkbox {
    accent-color: #f15e25 !important;
}

.razorpay-container .checkbox-label {
    color: #828282 !important;
    font-family: "Inter", sans-serif !important;
    font-size: 12px !important;
}

/* Loading states */
.razorpay-container .loading-text {
    color: #224520 !important;
    font-family: "Nunito Sans", sans-serif !important;
    font-weight: 600 !important;
}

/* Security badges */
.razorpay-container .security-badge {
    background: rgba(34, 69, 32, 0.05) !important;
    border: 1px solid rgba(34, 69, 32, 0.1) !important;
    border-radius: 6px !important;
    color: #224520 !important;
    font-family: "Inter", sans-serif !important;
    font-size: 11px !important;
}

/* Powered by Razorpay */
.razorpay-container .powered-by {
    opacity: 0.7 !important;
    transition: opacity 0.3s ease-in-out !important;
}

.razorpay-container .powered-by:hover {
    opacity: 1 !important;
}
</style>

<script>
    document.querySelectorAll('input[name="preset_amount"]').forEach(function(radio) {
        radio.addEventListener('change', function() {
            document.getElementById('donation_amount').value = this.value;
        });
    });

    document.getElementById("donateForm").addEventListener("submit", function(e) {
        e.preventDefault();
        const form = e.target;
        const formData = new FormData(form);
        const submitBtn = form.querySelector('button[type="submit"]');
        const originalBtnText = submitBtn.textContent;
        submitBtn.disabled = true;
        submitBtn.textContent = "Processing...";

        // Validate required fields before proceeding
        const donationAmount = formData.get('donation_amount');
        const userName = formData.get('name');
        const userEmail = formData.get('email');
        const userPhone = formData.get('phone');

        if (!donationAmount || donationAmount <= 0) {
            Swal.fire({
                icon: "warning",
                title: "💰 Amount Required",
                html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
                    <p style='margin-bottom: 15px; font-size: 16px;'>Please enter a valid donation amount.</p>
                    <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                        <p style='margin: 0; font-size: 14px; color: #828282;'>💡 You can select a preset amount or enter a custom amount</p>
                    </div>
                </div>`,
                background: "#FFFFFF",
                color: "#224520",
                confirmButtonColor: "#f15e25",
                confirmButtonText: "Got It",
                customClass: {
                    popup: 'swal2-popup-custom',
                    title: 'swal2-title-custom',
                    confirmButton: 'swal2-btn-custom'
                }
            });
            submitBtn.textContent = originalBtnText;
            submitBtn.disabled = false;
            return;
        }

        if (!userName || !userEmail || !userPhone) {
            Swal.fire({
                icon: "warning",
                title: "📝 Complete Your Details",
                html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
                    <p style='margin-bottom: 15px; font-size: 16px;'>Please fill in all required contact information.</p>
                    <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                        <p style='margin: 0; font-size: 14px; color: #828282;'>📋 Name, Email, and Phone number are required for donation receipt</p>
                    </div>
                </div>`,
                background: "#FFFFFF",
                color: "#224520",
                confirmButtonColor: "#f15e25",
                confirmButtonText: "Got It",
                customClass: {
                    popup: 'swal2-popup-custom',
                    title: 'swal2-title-custom',
                    confirmButton: 'swal2-btn-custom'
                }
            });
            submitBtn.textContent = originalBtnText;
            submitBtn.disabled = false;
            return;
        }

        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(userEmail)) {
            Swal.fire({
                icon: "warning",
                title: "📧 Invalid Email Format",
                html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
                    <p style='margin-bottom: 15px; font-size: 16px;'>Please enter a valid email address.</p>
                    <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                        <p style='margin: 0; font-size: 14px; color: #828282;'>✉️ Example: <EMAIL></p>
                    </div>
                </div>`,
                background: "#FFFFFF",
                color: "#224520",
                confirmButtonColor: "#f15e25",
                confirmButtonText: "Got It",
                customClass: {
                    popup: 'swal2-popup-custom',
                    title: 'swal2-title-custom',
                    confirmButton: 'swal2-btn-custom'
                }
            });
            submitBtn.textContent = originalBtnText;
            submitBtn.disabled = false;
            return;
        }

        // Validate phone number (basic validation for Indian numbers)
        const phoneRegex = /^[6-9]\d{9}$/;
        if (!phoneRegex.test(userPhone.replace(/\D/g, '').slice(-10))) {
            Swal.fire({
                icon: "warning",
                title: "📱 Invalid Phone Number",
                html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
                    <p style='margin-bottom: 15px; font-size: 16px;'>Please enter a valid 10-digit mobile number.</p>
                    <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                        <p style='margin: 0; font-size: 14px; color: #828282;'>📞 Example: 9876543210</p>
                    </div>
                </div>`,
                background: "#FFFFFF",
                color: "#224520",
                confirmButtonColor: "#f15e25",
                confirmButtonText: "Got It",
                customClass: {
                    popup: 'swal2-popup-custom',
                    title: 'swal2-title-custom',
                    confirmButton: 'swal2-btn-custom'
                }
            });
            submitBtn.textContent = originalBtnText;
            submitBtn.disabled = false;
            return;
        }

        // Get reCAPTCHA response and append to formData
        var recaptchaResponse = grecaptcha.getResponse();
        if (!recaptchaResponse) {
            Swal.fire({
                icon: "warning",
                title: "🤖 Security Verification Required",
                html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
                    <p style='margin-bottom: 15px; font-size: 16px;'>Please complete the reCAPTCHA verification.</p>
                    <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                        <p style='margin: 0; font-size: 14px; color: #828282;'>🔒 This helps us protect against automated submissions</p>
                    </div>
                    <p style='font-size: 14px; color: #224520; margin-top: 15px;'>Please scroll down and complete the verification, then try again.</p>
                </div>`,
                background: "#FFFFFF",
                color: "#224520",
                confirmButtonColor: "#f15e25",
                confirmButtonText: "Got It",
                customClass: {
                    popup: 'swal2-popup-custom',
                    title: 'swal2-title-custom',
                    confirmButton: 'swal2-btn-custom'
                },
                showClass: {
                    popup: 'animate__animated animate__fadeInUp'
                }
            });
            submitBtn.textContent = originalBtnText;
            submitBtn.disabled = false;
            return;
        }
        formData.append('g-recaptcha-response', recaptchaResponse);

        fetch("../webhook/donation-webhook.php", {
                method: "POST",
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && data.order_id && data.razorpay_key) {
                    // Get form data to pre-fill Razorpay
                    const formData = new FormData(form);
                    const userName = formData.get('name') || '';
                    const userEmail = formData.get('email') || '';
                    const userPhone = formData.get('phone') || '';

                    // Trigger Razorpay Checkout
                    var options = {
                        "key": data.razorpay_key,
                        "amount": data.amount,
                        "currency": data.currency,
                        "name": "UnnyanPath Foundation",
                        "description": "Support Our Mission - Your Donation Makes a Difference",
                        "image": "/static/images/logo/squre-logo.svg",
                        "order_id": data.order_id,
                        "prefill": {
                            "name": userName,
                            "email": userEmail,
                            "contact": userPhone
                        },
                        "theme": {
                            "color": "#f15e25",
                            "backdrop_color": "rgba(34, 69, 32, 0.8)"
                        },
                        "config": {
                            "display": {
                                "blocks": {
                                    "banks": {
                                        "name": "Pay using Net Banking",
                                        "instruments": [
                                            {
                                                "method": "netbanking",
                                                "banks": ["HDFC", "ICICI", "SBI", "AXIS", "KOTAK", "YESB"]
                                            }
                                        ]
                                    },
                                    "other": {
                                        "name": "Other Payment Methods",
                                        "instruments": [
                                            {
                                                "method": "card",
                                                "issuers": ["VISA", "MASTERCARD", "AMEX", "RUPAY"]
                                            },
                                            {
                                                "method": "upi"
                                            },
                                            {
                                                "method": "wallet",
                                                "wallets": ["paytm", "phonepe", "googlepay", "amazonpay"]
                                            }
                                        ]
                                    }
                                },
                                "sequence": ["block.banks", "block.other"],
                                "preferences": {
                                    "show_default_blocks": true
                                }
                            }
                        },
                        "handler": function (response){
                            // Get form data for webhook
                            const formData = new FormData(form);
                            const webhookData = {
                                razorpay_payment_id: response.razorpay_payment_id,
                                razorpay_order_id: response.razorpay_order_id,
                                razorpay_signature: response.razorpay_signature,
                                name: formData.get('name'),
                                email: formData.get('email'),
                                phone: formData.get('phone'),
                                pan: formData.get('text'), // PAN field
                                address: formData.get('address'),
                                message: formData.get('message'),
                                amount: data.amount / 100
                            };

                            fetch("../webhook/razorpay-webhook.php", {
                                method: "POST",
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify(webhookData)
                            }).then(() => {
                                // Show success message regardless of webhook response
                                Swal.fire({
                                    icon: "success",
                                    title: "🙏 Thank You for Your Generosity!",
                                    html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
                                        <p style='margin-bottom: 15px; font-size: 16px;'>Your donation has been processed successfully.</p>
                                        <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                                            <p style='margin: 5px 0; font-weight: 600;'>Payment ID: <span style='color: #f15e25; font-family: monospace;'>${response.razorpay_payment_id}</span></p>
                                            <p style='margin: 5px 0; font-size: 14px; color: #828282;'>Amount: ₹${data.amount / 100}</p>
                                        </div>
                                        <p style='font-size: 14px; color: #828282; margin-top: 15px;'>📧 You will receive a receipt via email shortly.</p>
                                        <p style='font-size: 14px; color: #224520; font-weight: 600; margin-top: 10px;'>Your support helps us make a meaningful impact! 💚</p>
                                    </div>`,
                                    background: "#FFFFFF",
                                    color: "#224520",
                                    confirmButtonColor: "#f15e25",
                                    confirmButtonText: "Continue",
                                    customClass: {
                                        popup: 'swal2-popup-custom',
                                        title: 'swal2-title-custom',
                                        confirmButton: 'swal2-btn-custom'
                                    },
                                    showClass: {
                                        popup: 'animate__animated animate__fadeInUp'
                                    }
                                });
                            }).catch(() => {
                                // Show success message even if webhook fails
                                Swal.fire({
                                    icon: "success",
                                    title: "🙏 Thank You for Your Generosity!",
                                    html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
                                        <p style='margin-bottom: 15px; font-size: 16px;'>Your donation has been processed successfully.</p>
                                        <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                                            <p style='margin: 5px 0; font-weight: 600;'>Payment ID: <span style='color: #f15e25; font-family: monospace;'>${response.razorpay_payment_id}</span></p>
                                            <p style='margin: 5px 0; font-size: 14px; color: #828282;'>Amount: ₹${data.amount / 100}</p>
                                        </div>
                                        <p style='font-size: 14px; color: #828282; margin-top: 15px;'>📧 You will receive a receipt via email shortly.</p>
                                        <p style='font-size: 14px; color: #224520; font-weight: 600; margin-top: 10px;'>Your support helps us make a meaningful impact! 💚</p>
                                    </div>`,
                                    background: "#FFFFFF",
                                    color: "#224520",
                                    confirmButtonColor: "#f15e25",
                                    confirmButtonText: "Continue",
                                    customClass: {
                                        popup: 'swal2-popup-custom',
                                        title: 'swal2-title-custom',
                                        confirmButton: 'swal2-btn-custom'
                                    },
                                    showClass: {
                                        popup: 'animate__animated animate__fadeInUp'
                                    }
                                });
                            });

                            form.reset();
                            submitBtn.disabled = false;
                            submitBtn.textContent = "Donate Now";
                        },
                        "modal": {
                            "ondismiss": function() {
                                Swal.fire({
                                    icon: "info",
                                    title: "Payment Cancelled",
                                    html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
                                        <p style='margin-bottom: 15px; font-size: 16px;'>You cancelled the payment process.</p>
                                        <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                                            <p style='margin: 0; font-size: 14px; color: #828282;'>💳 No money was deducted from your account</p>
                                        </div>
                                        <p style='font-size: 14px; color: #224520; margin-top: 15px;'>You can try again anytime to support our mission! 🤝</p>
                                    </div>`,
                                    background: "#FFFFFF",
                                    color: "#224520",
                                    confirmButtonColor: "#f15e25",
                                    confirmButtonText: "Try Again",
                                    customClass: {
                                        popup: 'swal2-popup-custom',
                                        title: 'swal2-title-custom',
                                        confirmButton: 'swal2-btn-custom'
                                    },
                                    showClass: {
                                        popup: 'animate__animated animate__fadeInUp'
                                    }
                                });
                                submitBtn.disabled = false;
                                submitBtn.textContent = "Donate Now";
                            },
                            "escape": true,
                            "backdropclose": true
                        },
                        "retry": {
                            "enabled": true,
                            "max_count": 2
                        },
                        "remember_customer": false,
                        "readonly": {
                            "email": true,
                            "contact": true,
                            "name": true
                        },
                        "send_sms_hash": true,
                        "allow_rotation": true,
                        "timeout": 900
                    };
                    var rzp1 = new Razorpay(options);
                    rzp1.open();
                } else {
                    Swal.fire({
                        icon: "error",
                        title: "⚠️ Payment Processing Error",
                        html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
                            <p style='margin-bottom: 15px; font-size: 16px;'>We encountered an issue processing your donation.</p>
                            <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                                <p style='margin: 0; font-size: 14px; color: #f15e25; font-weight: 600;'>${data.error || "Submission failed. Please try again."}</p>
                            </div>
                            <p style='font-size: 14px; color: #828282; margin-top: 15px;'>💡 Please check your details and try again, or contact us for assistance.</p>
                        </div>`,
                        background: "#FFFFFF",
                        color: "#224520",
                        confirmButtonColor: "#f15e25",
                        confirmButtonText: "Try Again",
                        customClass: {
                            popup: 'swal2-popup-custom',
                            title: 'swal2-title-custom',
                            confirmButton: 'swal2-btn-custom'
                        },
                        showClass: {
                            popup: 'animate__animated animate__fadeInUp'
                        }
                    });
                }
            })
            .catch(() => {
                Swal.fire({
                    icon: "error",
                    title: "🔌 Connection Error",
                    html: `<div style='color:#224520; font-family: "Inter", sans-serif; line-height: 1.6;'>
                        <p style='margin-bottom: 15px; font-size: 16px;'>Unable to connect to our payment system.</p>
                        <div style='background: #F8F8F8; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                            <p style='margin: 0; font-size: 14px; color: #828282;'>🌐 Please check your internet connection and try again</p>
                        </div>
                        <p style='font-size: 14px; color: #224520; margin-top: 15px;'>If the problem persists, please contact our support team.</p>
                    </div>`,
                    background: "#FFFFFF",
                    color: "#224520",
                    confirmButtonColor: "#f15e25",
                    confirmButtonText: "Retry",
                    customClass: {
                        popup: 'swal2-popup-custom',
                        title: 'swal2-title-custom',
                        confirmButton: 'swal2-btn-custom'
                    },
                    showClass: {
                        popup: 'animate__animated animate__fadeInUp'
                    }
                });
            })
            .finally(() => {
                submitBtn.textContent = originalBtnText;
                submitBtn.disabled = false;
            });
    });
</script>
