<?php
/**
 * Hostinger-Optimized Configuration
 * Secure configuration loader for Hostinger hosting environment
 */

// Hostinger-compatible environment variable loader
function getHostingerEnv($key, $default = null) {
    // Method 1: Try system environment variables (hPanel Environment Variables)
    $value = getenv($key);
    if ($value !== false && $value !== '') {
        return $value;
    }
    
    // Method 2: Try $_ENV superglobal
    if (isset($_ENV[$key]) && $_ENV[$key] !== '') {
        return $_ENV[$key];
    }
    
    // Method 3: Try $_SERVER superglobal
    if (isset($_SERVER[$key]) && $_SERVER[$key] !== '') {
        return $_SERVER[$key];
    }
    
    // Method 4: Load from .env file (one-time load)
    static $envLoaded = false;
    if (!$envLoaded) {
        $envFile = $_SERVER['DOCUMENT_ROOT'] . '/.env';
        if (file_exists($envFile) && is_readable($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line) || strpos($line, '#') === 0) {
                    continue; // Skip empty lines and comments
                }
                
                if (strpos($line, '=') !== false) {
                    list($name, $value) = explode('=', $line, 2);
                    $name = trim($name);
                    $value = trim($value, " \t\n\r\0\x0B\"'"); // Remove quotes
                    
                    if (!empty($name)) {
                        putenv("$name=$value");
                        $_ENV[$name] = $value;
                        $_SERVER[$name] = $value;
                    }
                }
            }
        }
        $envLoaded = true;
    }
    
    // Try again after loading .env
    $value = getenv($key);
    return ($value !== false && $value !== '') ? $value : $default;
}

// Validate critical environment variables
function validateHostingerEnv() {
    $required = [
        'RAZORPAY_KEY_ID',
        'RAZORPAY_KEY_SECRET', 
        'MAIL_USERNAME',
        'MAIL_PASSWORD',
        'RECAPTCHA_SITE_KEY',
        'RECAPTCHA_SECRET_KEY'
    ];
    
    $missing = [];
    foreach ($required as $var) {
        if (empty(getHostingerEnv($var))) {
            $missing[] = $var;
        }
    }
    
    if (!empty($missing) && getHostingerEnv('ENVIRONMENT') !== 'development') {
        error_log('UnnyanPath: Missing environment variables: ' . implode(', ', $missing));
        // Don't die in production, use fallback values
    }
    
    return empty($missing);
}

// Hostinger session configuration
function setupHostingerSession() {
    // Secure session settings for Hostinger
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_samesite', 'Strict');
    
    // Enable secure cookies if HTTPS is available
    if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
        ini_set('session.cookie_secure', 1);
    }
    
    // Start session if not already started
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
}

// Hostinger error reporting configuration
function setupHostingerErrorReporting() {
    $environment = getHostingerEnv('ENVIRONMENT', 'production');
    
    if ($environment === 'production') {
        // Production settings
        ini_set('display_errors', 0);
        ini_set('log_errors', 1);
        ini_set('error_log', $_SERVER['DOCUMENT_ROOT'] . '/logs/php-errors.log');
        error_reporting(E_ALL & ~E_NOTICE & ~E_DEPRECATED & ~E_STRICT);
    } else {
        // Development settings
        ini_set('display_errors', 1);
        ini_set('log_errors', 1);
        error_reporting(E_ALL);
    }
}

// Initialize Hostinger environment
validateHostingerEnv();
setupHostingerSession();
setupHostingerErrorReporting();

// Define application constants
if (!defined('RAZORPAY_KEY_ID'))
    define('RAZORPAY_KEY_ID', getHostingerEnv('RAZORPAY_KEY_ID', 'rzp_test_Ntm50q1K9CRwEG'));
if (!defined('RAZORPAY_KEY_SECRET'))
    define('RAZORPAY_KEY_SECRET', getHostingerEnv('RAZORPAY_KEY_SECRET', 'V9vIpDkk2M5G1tTQMEXMlG1u'));

// Mail configuration for Hostinger SMTP
if (!defined('MAIL_HOST'))
    define('MAIL_HOST', getHostingerEnv('MAIL_HOST', 'smtp.hostinger.com'));
if (!defined('MAIL_USERNAME'))
    define('MAIL_USERNAME', getHostingerEnv('MAIL_USERNAME', '<EMAIL>'));
if (!defined('MAIL_PASSWORD'))
    define('MAIL_PASSWORD', getHostingerEnv('MAIL_PASSWORD', 'No-reply@UPF#003/-'));
if (!defined('MAIL_ENCRYPTION'))
    define('MAIL_ENCRYPTION', getHostingerEnv('MAIL_ENCRYPTION', 'ssl'));
if (!defined('MAIL_PORT'))
    define('MAIL_PORT', getHostingerEnv('MAIL_PORT', 465));

// reCAPTCHA configuration
if (!defined('RECAPTCHA_SITE_KEY'))
    define('RECAPTCHA_SITE_KEY', getHostingerEnv('RECAPTCHA_SITE_KEY', '6LcexmcrAAAAAFnHilP7rN2Txii7IA78dHZlY9fW'));
if (!defined('RECAPTCHA_SECRET_KEY'))
    define('RECAPTCHA_SECRET_KEY', getHostingerEnv('RECAPTCHA_SECRET_KEY', '6LcexmcrAAAAAEG4t-u7f56kkpnv2SaWb6JJJq9V'));

// Base URL configuration
if (!defined('base_url'))
    define('base_url', getHostingerEnv('BASE_URL', 'https://unnyanpathfoundation.in/'));
if (!defined('base_app'))
    define('base_app', str_replace('\\', '/', __DIR__) . '/');

// Environment detection
if (!defined('IS_PRODUCTION'))
    define('IS_PRODUCTION', getHostingerEnv('ENVIRONMENT', 'production') === 'production');

// Hostinger-specific security settings
if (!defined('HOSTINGER_SECURITY_ENABLED'))
    define('HOSTINGER_SECURITY_ENABLED', true);

// Rate limiting configuration
if (!defined('RATE_LIMIT_ENABLED'))
    define('RATE_LIMIT_ENABLED', getHostingerEnv('RATE_LIMIT_ENABLED', 'true') === 'true');

// Debug mode
if (!defined('DEBUG_MODE'))
    define('DEBUG_MODE', getHostingerEnv('DEBUG_MODE', 'false') === 'true');

// Log successful configuration load
if (DEBUG_MODE) {
    error_log('UnnyanPath: Hostinger configuration loaded successfully');
}

// Security: Prevent direct access to this file
if (basename($_SERVER['PHP_SELF']) === basename(__FILE__)) {
    http_response_code(403);
    exit('Direct access not allowed');
}
?>
